{"mcpServers": {"amap": {"command": "npx", "args": ["-y", "@amap/amap-maps-mcp-server"], "env": {"AMAP_MAPS_API_KEY": ""}}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest", "--headless"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "elasticsearch-mcp-server": {"command": "npx", "args": ["-y", "@elastic/mcp-server-elasticsearch"], "env": {"ES_URL": ""}}}}