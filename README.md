# YM MCP Server

YM MCP Server 是一个基于 FastMCP 框架构建的 Elasticsearch 管理服务，提供对 Elasticsearch/OpenSearch 集群的索引查询和管理功能。

## 功能特性

- 列出所有 Elasticsearch 索引
- 获取特定索引的详细信息
- 执行 Elasticsearch 查询操作
- 获取指定时区的当前时间

## 项目结构

```text
src/ 
├── config/                             # 配置文件 
│ ├── config_settings.py                # 项目配置 
├── logs/                               # 日志
├── mcp_client/                         # MCP 客户端实现 
├── mcp_server/                         # MCP 服务实现 
│ └── es_server.py                      # Elasticsearch 工具实现 
├── utils/                              # 工具类 
│ ├── es_client.py                      # Elasticsearch 客户端 
│ └── my_log.py                         # 日志工具 
└── main.py                             # 程序入口
```

## 环境要求

- Python 3.11+
- Elasticsearch 或 OpenSearch 集群
- Poetry（用于依赖管理）

## 安装步骤

1. 克隆项目到本地环境
2. 使用 Poetry 安装依赖：
```text
# 安装 poetry
pip install poetry
# 安装依赖
poetry install
```

## 配置管理
- 复制.env.example 文件为 .env，并修改ES配置。

## 运行方式

### 本地运行
```text
python src/main.py
```
### Docker 运行
```text
# 构建镜像
docker build -t ym_mcp_server .  
# 运行容器
docker run -d -p 8001:8001 ym_mcp_server
```
