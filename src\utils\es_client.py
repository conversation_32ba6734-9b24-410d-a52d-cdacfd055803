#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: es_client.py 
@time: 2025/8/26 14:32
@Description: 
"""
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from fastmcp import FastMCP
from opensearchpy import Async<PERSON>penSearch, AsyncHttpConnection
from opensearchpy.exceptions import ConnectionError, TransportError, OpenSearchException

from src.config.config_settings import settings
from src.utils.my_log import logger


def create_es_client() -> AsyncOpenSearch:
    """
    创建 AsyncOpenSearch 客户端实例
    """
    config = {
        "hosts": [settings.es_host],
        "timeout": settings.timeout,
        "max_retries": settings.max_retries,
        "retry_on_timeout": True,
        "connection_class": AsyncHttpConnection,
        "pool_maxsize": settings.pool_maxsize,
    }

    if settings.es_username and settings.es_password:
        config["http_auth"] = (settings.es_username, settings.es_password)

    try:
        client = AsyncOpenSearch(**config)
        logger.info(f"✅ Elasticsearch client created for host: {settings.es_host}")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to create Elasticsearch client: {str(e)}")
        raise


@asynccontextmanager
async def lifespan_es(mcp: FastMCP) -> AsyncGenerator[AsyncOpenSearch, None]:
    """
    FastMCP 的生命周期管理器：创建并安全关闭 Elasticsearch 客户端。

    保证在应用启动时建立连接，在关闭时正确释放资源。
    """
    es: Optional[AsyncOpenSearch] = None
    try:
        es = create_es_client()

        # 检查连接健康状态
        try:
            if await es.ping():
                logger.info("🟢 Elasticsearch ping succeeded. Connection is healthy.")
            else:
                logger.warning("🟠 Elasticsearch ping succeeded but returned False. Proceeding with caution.")
        except (ConnectionError, TransportError) as e:
            logger.warning(f"🟠 Failed to ping Elasticsearch: {str(e)}. Proceeding anyway...")
        except Exception as e:
            logger.warning(f"🟠 Unexpected error during ping: {str(e)}. Will still yield client.")

        # ✅ 将客户端交给 FastMCP 请求上下文
        yield es

    except OpenSearchException as e:
        logger.error(f"❌ Elasticsearch service exception during lifespan setup: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error setting up Elasticsearch client: {str(e)}")
        raise
    finally:
        # 安全关闭客户端
        if es is not None:
            try:
                await es.close()
                logger.info("🔒 Elasticsearch client closed successfully.")
            except Exception as e:
                logger.error(f"❌ Error closing Elasticsearch client: {str(e)}")
        else:
            logger.debug("No Elasticsearch client to close.")
