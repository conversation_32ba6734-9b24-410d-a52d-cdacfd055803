#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: es_server.py
@time: 2025/8/25 16:41
@Description: 
"""
from datetime import datetime
from typing import Optional

import pytz
from fastmcp import FastMCP, Context
from src.utils.es_client import lifespan_es
from src.config.config_settings import settings

mcp = FastMCP('YM_MCP', lifespan=lifespan_es)


@mcp.tool()
async def list_elasticsearch_indices(
        ctx: Context
) -> list[str]:
    """
    获取 Elasticsearch 集群中的所有索引名称。

    返回集群中所有可用索引的名称列表，可用于查询或其他操作。

    Args:
        ctx (Context): 请求上下文，包含 Elasticsearch 客户端。

    Returns:
        list[str]: 索引名称列表，例如 ["logs-2024", "users", "metrics-2025"]。

    Raises:
        Exception: 如果 Elasticsearch 集群不可用或无索引，返回空列表或抛出异常。
    """
    es = ctx.request_context.lifespan_context
    indices = await es.cat.indices(format='json')
    return [i['index'] for i in indices]


@mcp.tool()
async def get_elasticsearch_index_info(
        index_name: str,
        ctx: Context
) -> dict:
    """
    获取指定 Elasticsearch 索引的配置和元数据信息。

    返回索引的 settings、mappings 和 aliases 等信息，用于检查索引结构或调试。

    Args:
        index_name (str): 要查询的索引名称，例如 "logs-2024-04"。
        ctx (Context): 请求上下文，包含 Elasticsearch 客户端。

    Returns:
        dict: 索引信息字典，包含 settings、mappings 和 aliases。
              示例：{"logs-2024": {"settings": {...}, "mappings": {...}, "aliases": {...}}}

    Raises:
        Exception: 如果索引不存在或查询失败，将抛出异常。
    """
    es = ctx.request_context.lifespan_context
    return await es.indices.get(index=index_name)


@mcp.tool()
async def search_elasticsearch(
    index_name: str,
    query_body: dict,
    ctx: Context
) -> dict:
    """"
    在指定 Elasticsearch 索引中执行搜索查询。

    支持全文搜索、过滤和聚合等操作，使用 Elasticsearch 查询格式。

    Args:
        index_name (str): 要搜索的索引名称，支持通配符，如 "logs-*"。
        query_body (dict): 查询体，格式符合 Elasticsearch 查询规范。
                           示例：{"query": {"match": {"message": "error"}}, "size": 10}
        ctx (Context): 请求上下文，包含 Elasticsearch 客户端。

    Returns:
        dict: 搜索结果，包含命中文档、耗时等信息。
              示例：{"took": 15, "timed_out": False, "hits": {"total": {"value": 5}, "hits": [...]}}}

    Raises:
        Exception: 如果查询语法错误或索引不存在，将抛出异常。
    """
    es = ctx.request_context.lifespan_context
    return await es.search(index=index_name, body=query_body)


@mcp.tool()
async def get_current_time(
        timezone: Optional[str] = "Asia/Shanghai"
) -> str:
    """
    获取指定时区的当前日期和时间。

    支持 IANA 时区名称（如 'America/New_York'）或别名（如 '北京', 'CST'）。默认时区为 Asia/Shanghai。

    Args:
        timezone (str, optional): 目标时区名称或别名，默认 "Asia/Shanghai"。

    Returns:
        str: 格式化的时间字符串，格式为 “{时区} 的当前时间是: YYYY年MM月DD日 HH:MM:SS TZ”。
             示例：北京 的当前时间是: 2025年04月05日 14:23:10 CST

    Raises:
        pytz.exceptions.UnknownTimeZoneError: 如果提供的时区名称无效。
        Exception: 其他意外错误。
    """
    try:
        timezone_mapping = {
            "中国": "Asia/Shanghai",
            "北京": "Asia/Shanghai",
            "上海": "Asia/Shanghai",
            "China": "Asia/Shanghai",
            "Beijing": "Asia/Shanghai",
            "CST": "Asia/Shanghai"
        }

        actual_timezone = timezone_mapping.get(timezone, timezone)
        tz = pytz.timezone(actual_timezone)
        current_time = datetime.now(tz)
        formatted_time = current_time.strftime("%Y年%m月%d日 %H:%M:%S %Z")
        return f"{timezone} 的当前时间是: {formatted_time}"
    except pytz.exceptions.UnknownTimeZoneError:
        return f"错误: 未知的时区 '{timezone}'。请提供有效的时区名称。"
    except Exception as e:
        return f"获取时间时出错: {str(e)}"


async def main():
    await mcp.run_async(
        transport=settings.transport,
        host=settings.mcp_server_host,
        port=settings.mcp_server_port
    )


