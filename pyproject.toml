[project]
name = "ym_mcp_server"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON>",email = "<PERSON><PERSON><PERSON><PERSON>@scmttec.com"}
]
readme = "README.md"
requires-python = ">=3.11,<4.0"
dependencies = [
    "fastmcp (>=2.11.3,<3.0.0)",
    "pytz (>=2025.2,<2026.0)",
    "elasticsearch (>=9.1.0,<10.0.0)",
    "opensearch-py (>=3.0.0,<4.0.0)",
    "aiohttp (>=3.12.15,<4.0.0)"
]

[tool.poetry]
packages = [
    {include = "mcp_server", from = "src"},
    {include = "mcp_client", from = "src"},
    {include = "utils", from = "src"},
    {include = "config", from = "src"}
]

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
