#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: my_log.py
@time: 2025/08/20 9:25
@Description: 
"""
import logging.config
import os
from typing import Optional, Dict, Any
from functools import lru_cache
from pathlib import Path

from src.config.config_settings import settings


class LogConfig:

    DEFAULT_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s - %(message)s'
    PERFORMANCE_FORMAT = '%(asctime)s - PERF - %(levelname)s - %(message)s'
    BUSINESS_FORMAT = '%(asctime)s - BIZ - %(levelname)s - %(message)s'
    ERROR_FORMAT = '%(asctime)s - ERROR - %(name)s - [%(filename)s:%(lineno)d] - %(funcName)s - %(message)s'
    DEFAULT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

    @staticmethod
    def get_logging_config(
            log_path: str = settings.log_path,  # 日志路径
            log_level: str = "INFO",  # INFO
            backup_count: int = 30,  # 30天
            max_bytes: int = 10 * 1024 * 1024,  # 10MB
    ) -> Dict[str, Any]:

        Path(log_path).mkdir(parents=True, exist_ok=True)

        return {
            'version': 1,
            'disable_existing_loggers': False,  # 避免禁用其他模块的logger
            'formatters': {
                'detailed': {
                    'format': LogConfig.DEFAULT_FORMAT,
                    'datefmt': LogConfig.DEFAULT_DATE_FORMAT
                },
                'performance': {
                    'format': LogConfig.PERFORMANCE_FORMAT,
                    'datefmt': LogConfig.DEFAULT_DATE_FORMAT
                },
                'business': {
                    'format': LogConfig.BUSINESS_FORMAT,
                    'datefmt': LogConfig.DEFAULT_DATE_FORMAT
                },
                'error': {
                    'format': LogConfig.ERROR_FORMAT,
                    'datefmt': LogConfig.DEFAULT_DATE_FORMAT
                },
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': log_level,
                    'formatter': 'detailed',
                    'stream': 'ext://sys.stdout',
                },
                'file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'DEBUG',
                    'formatter': 'detailed',
                    'filename': os.path.join(log_path, "app.log"),
                    'maxBytes': max_bytes,
                    'backupCount': backup_count,
                    'encoding': 'utf8',
                },
                'error_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'ERROR',
                    'formatter': 'error',
                    'filename': os.path.join(log_path, "error.log"),
                    'maxBytes': max_bytes,
                    'backupCount': backup_count,
                    'encoding': 'utf8',
                },
                'performance_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'INFO',
                    'formatter': 'performance',
                    'filename': os.path.join(log_path, "performance.log"),
                    'maxBytes': max_bytes,
                    'backupCount': backup_count,
                    'encoding': 'utf8',
                },
                'business_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'INFO',
                    'formatter': 'business',
                    'filename': os.path.join(log_path, "business.log"),
                    'maxBytes': max_bytes,
                    'backupCount': backup_count,
                    'encoding': 'utf8',
                }
            },
            'loggers': {
                '': {  # root logger
                    'level': log_level,
                    'handlers': ['console', 'file', 'error_file'],
                    'propagate': False
                },
                'app': {  # 应用logger
                    'level': log_level,
                    'handlers': ['console', 'file', 'error_file'],
                    'propagate': False,
                },
                'performance': {  # 性能logger
                    'level': 'INFO',
                    'handlers': ['performance_file'],
                    'propagate': False,
                },
                'business': {  # 业务logger
                    'level': 'INFO',
                    'handlers': ['business_file'],
                    'propagate': False,
                }
            }
        }


@lru_cache(maxsize=settings.max_size)
def get_logger(name: Optional[str] = None) -> logging.Logger:

    if not name:
        name = __name__

    if not hasattr(get_logger, "initialized"):
        logging.config.dictConfig(LogConfig.get_logging_config())
        setattr(get_logger, "initialized", True)

    return logging.getLogger(name)


logger = get_logger()