#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: config_settings.py 
@time: 2025/8/27 16:02
@Description: 
"""
import os

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import find_dotenv

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

class Settings(BaseSettings):
    base_dir: str = Field(default=BASE_DIR)
    es_host: str = Field(default='127.0.0.1', description='host server')
    es_username: str = Field(default='admin', description='es_username')
    es_password: str = Field(default='123456', description='es_password')
    timeout: int = Field(default=30, description='timeout')
    max_retries: int = Field(default=3, description='max_retries')
    pool_maxsize: int = Field(default=10, description='pool_maxsize')
    max_size: int = Field(default=100, description='max_size')
    transport: str = Field(default='stdio', description='transport')
    mcp_server_host: str = Field(default='127.0.0.1', description='mcp_server_host')
    mcp_server_port: int = Field(default=8000, description='mcp_server_port')

    model_config = SettingsConfigDict(
        env_file=find_dotenv(),
        env_file_encoding='utf-8',
        case_sensitive=False,
        extra='ignore'
    )

    @property
    def log_path(self) -> str:
        path = os.path.join(self.base_dir, "logs")
        os.makedirs(path, exist_ok=True)
        return path

settings = Settings()
